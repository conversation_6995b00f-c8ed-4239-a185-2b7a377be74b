{"name": "wx-nan-server", "version": "1.0.0", "description": "Backend server for wx-nan WeChat Mini Program", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "generate": "prisma generate", "migrate": "prisma migrate dev", "studio": "prisma studio", "db:push": "prisma db push", "db:test": "node test-db.js", "db:export": "node scripts/export-data.js", "db:import": "node scripts/import-data.js", "db:clear": "node scripts/clear-data.js", "db:seed": "node seed-test-data.js", "db:seed-complete": "node seed-complete-data.js", "db:seed-messages": "node seed-messages.js", "test:api": "node test-api.js", "test:miniprogram": "node test-miniprogram-api.js", "quick-start": "node scripts/quick-start.js"}, "keywords": ["express", "api", "wechat", "mini-program"], "author": "", "license": "ISC", "dependencies": {"@prisma/client": "^6.8.2", "axios": "^1.4.0", "bcrypt": "^5.1.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1"}, "devDependencies": {"nodemon": "^3.0.1", "prisma": "^6.8.2"}}