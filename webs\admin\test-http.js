import http from "http";

console.log("🔍 测试HTTP连接到 localhost:3001...");

const req = http.request(
  {
    hostname: "localhost",
    port: 3001,
    path: "/",
    method: "GET",
    timeout: 5000
  },
  res => {
    console.log(`状态码: ${res.statusCode}`);
    console.log(`响应头:`, res.headers);

    let data = "";
    res.on("data", chunk => {
      data += chunk;
    });

    res.on("end", () => {
      console.log(`响应内容长度: ${data.length} 字节`);
      if (data.length > 0) {
        console.log(`响应内容预览:`);
        console.log(data.substring(0, 500));
      }
    });
  }
);

req.on("error", err => {
  console.log(`请求失败: ${err.message}`);
});

req.on("timeout", () => {
  console.log(`请求超时`);
  req.destroy();
});

req.end();
