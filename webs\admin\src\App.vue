<template>
  <el-config-provider :locale="currentLocale">
    <router-view />
    <ReDialog />
  </el-config-provider>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import { checkVersion, unCheckVersion } from "version-rocket";
import { ElConfigProvider } from "element-plus";
import en from "element-plus/dist/locale/en.mjs";
import { ReDialog } from "@/components/ReDialog";
import zhCn from "element-plus/dist/locale/zh-cn.mjs";

export default defineComponent({
  name: "app",
  components: {
    [ElConfigProvider.name]: ElConfigProvider,
    ReDialog
  },
  computed: {
    currentLocale() {
      return this.$storage.locale?.locale === "zh" ? zhCn : en;
    }
  },
  beforeCreate() {
    const { version, name: title } = __APP_INFO__.pkg; //再vite.config.js注入的
    const { VITE_PUBLIC_PATH, MODE } = import.meta.env;
    // console.log(VITE_PUBLIC_PATH,MODE ,`${location.origin}${VITE_PUBLIC_PATH}version.json`)
    if (MODE === "production") {
      //打包的时候会执行 generate-version-file 命令 有配置
      // 版本实时更新检测，只作用于线上环境----在打开网页很久 然后更新了的提示
      checkVersion(
        // config
        {
          // 5分钟检测一次版本
          pollingTime: 300000,
          localPackageVersion: version,
          originVersionFileUrl: `${location.origin}${VITE_PUBLIC_PATH}version.json`
        },
        // options
        {
          //弹窗
          title,
          description: "检测到新版本",
          buttonText: "立即更新"
        }
      );
    }
  },
  unmounted() {
    if (import.meta.env.MODE === "production") {
      unCheckVersion({ closeDialog: false }); // 终止版本检测
    }
  }
});
</script>
