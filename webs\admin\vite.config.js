import { loadEnv } from 'vite';
import { warpperEnv } from "./build";
import { resolve } from "path";
import { getPluginsList } from "./build/pulgins";
import { include, exclude } from "./build/optimize";
import pkg from "./package.json";
import dayjs from "dayjs";
/** 当前执行node命令时文件夹的地址（工作目录） */
const root = process.cwd();
/** 路径查找 */
const pathResolve = (dir) => {
    return resolve(__dirname, ".", dir);
};
/** 设置别名 */
const alias = {
    "@": pathResolve("src"),
    "@build": pathResolve("build")
};
// https://vitejs.dev/config/
export default ({ command, mode }) => {
    // console.log(loadEnv(mode, root))
    const { VITE_CDN, VITE_PORT, VITE_COMPRESSION, VITE_PUBLIC_PATH } = warpperEnv(loadEnv(mode, root));
    const { dependencies, devDependencies, name, version } = pkg;
    const __APP_INFO__ = {
        pkg: { dependencies, devDependencies, name, version },
        lastBuildTime: dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss")
    };
    return {
        base: VITE_PUBLIC_PATH,
        root,
        resolve: {
            alias
        },
        // 服务端渲染
        server: {
            // 端口号
            port: VITE_PORT,
            host: "0.0.0.0",
            // 本地跨域代理 https://cn.vitejs.dev/config/server-options.html#server-proxy
            proxy: {},
            // 预热文件以提前转换和缓存结果，降低启动期间的初始页面加载时长并防止转换瀑布
            warmup: {
                clientFiles: ["./index.html", "./src/{views,components}/*"]
            }
        },
        optimizeDeps: {
            include,
            exclude
        },
        build: {
            // https://cn.vitejs.dev/guide/build.html#browser-compatibility
            target: "es2015",
            sourcemap: false,
            // 消除打包大小超过500kb警告
            chunkSizeWarningLimit: 4000,
            rollupOptions: {
                input: {
                    index: pathResolve("index.html")
                },
                // 静态资源分类打包
                output: {
                    chunkFileNames: "static/js/[name]-[hash].js",
                    entryFileNames: "static/js/[name]-[hash].js",
                    assetFileNames: "static/[ext]/[name]-[hash].[ext]"
                }
            }
        },
        plugins: getPluginsList(VITE_CDN, VITE_COMPRESSION),
        define: {
            __INTLIFY_PROD_DEVTOOLS__: false,
            __APP_INFO__: JSON.stringify(__APP_INFO__)
        }
    };
};
//# sourceMappingURL=vite.config.js.map