import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/layout/index.vue'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: { title: '登录' }
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        meta: { title: '仪表盘', icon: 'dashboard' }
      }
    ]
  },
  {
    path: '/menu',
    component: Layout,
    redirect: '/menu/list',
    meta: { title: '菜单管理', icon: 'menu' },
    children: [
      {
        path: 'list',
        name: 'MenuList',
        component: () => import('@/views/menu/list.vue'),
        meta: { title: '菜单列表', icon: 'list' }
      },
      {
        path: 'dishes',
        name: 'DishList',
        component: () => import('@/views/menu/dishes.vue'),
        meta: { title: '菜品管理', icon: 'dish' }
      },
      {
        path: 'categories',
        name: 'CategoryList',
        component: () => import('@/views/menu/categories.vue'),
        meta: { title: '分类管理', icon: 'category' }
      }
    ]
  },
  {
    path: '/order',
    component: Layout,
    redirect: '/order/list',
    meta: { title: '订单管理', icon: 'order' },
    children: [
      {
        path: 'list',
        name: 'OrderList',
        component: () => import('@/views/order/list.vue'),
        meta: { title: '订单列表', icon: 'list' }
      },
      {
        path: 'today',
        name: 'TodayOrder',
        component: () => import('@/views/order/today.vue'),
        meta: { title: '今日订单', icon: 'today' }
      }
    ]
  },
  {
    path: '/message',
    component: Layout,
    redirect: '/message/list',
    meta: { title: '消息管理', icon: 'message' },
    children: [
      {
        path: 'list',
        name: 'MessageList',
        component: () => import('@/views/message/list.vue'),
        meta: { title: '家庭留言', icon: 'message' }
      },
      {
        path: 'notification',
        name: 'NotificationList',
        component: () => import('@/views/message/notification.vue'),
        meta: { title: '系统通知', icon: 'notification' }
      }
    ]
  },
  {
    path: '/user',
    component: Layout,
    redirect: '/user/list',
    meta: { title: '用户管理', icon: 'user' },
    children: [
      {
        path: 'list',
        name: 'UserList',
        component: () => import('@/views/user/list.vue'),
        meta: { title: '用户列表', icon: 'list' }
      }
    ]
  },
  {
    path: '/statistics',
    component: Layout,
    redirect: '/statistics/overview',
    meta: { title: '统计分析', icon: 'chart' },
    children: [
      {
        path: 'overview',
        name: 'StatisticsOverview',
        component: () => import('@/views/statistics/overview.vue'),
        meta: { title: '数据概览', icon: 'overview' }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 楠楠家厨管理系统`
  }
  
  // 检查登录状态
  const token = localStorage.getItem('admin_token')
  
  if (to.path === '/login') {
    next()
  } else if (!token) {
    next('/login')
  } else {
    next()
  }
})

export default router
