<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="e6e3c576-6ca4-47be-970f-dd5a29b40234" name="更改" comment="24.2.19">
      <change beforePath="$PROJECT_DIR$/src/views/components/dialog/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/components/dialog/index.vue" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/src/views/lx/fragment/onther/gsapDemo.vue" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="PackageJsonUpdateNotifier">
    <dismissed value="$PROJECT_DIR$/package.json" />
  </component>
  <component name="ProjectId" id="2b3zH18cQs3mGq6OHOQvGREWmec" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ASKED_ADD_EXTERNAL_FILES&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;js.linters.configure.manually.selectedeslint&quot;: &quot;true&quot;,
    &quot;js.linters.configure.manually.selectedtslint&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Desktop/pure-style/pure_hl&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.standard&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.stylelint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;C:\\Users\\<USER>\\Desktop\\pure-style\\pure_hl\\node_modules\\eslint&quot;,
    &quot;node.js.selected.package.standard&quot;: &quot;&quot;,
    &quot;node.js.selected.package.stylelint&quot;: &quot;C:\\Users\\<USER>\\Desktop\\学习\\pure-style\\pure_hl\\node_modules\\stylelint&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;C:\\Users\\<USER>\\Desktop\\pure-style\\pure_hl&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;pnpm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;ts.external.directory.path&quot;: &quot;C:\\Users\\<USER>\\Desktop\\学习\\pure-style\\pure_hl\\node_modules\\typescript\\lib&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\pure-style\pure_hl\src\assets\lx" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="dev" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="npm.dev" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="e6e3c576-6ca4-47be-970f-dd5a29b40234" name="更改" comment="" />
      <created>1705458468547</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1705458468547</updated>
      <workItem from="1705458474034" duration="760000" />
      <workItem from="1705471212228" duration="6115000" />
      <workItem from="1705478295349" duration="9449000" />
      <workItem from="1705540983136" duration="2628000" />
      <workItem from="1705544615508" duration="19140000" />
      <workItem from="1705626512871" duration="10870000" />
      <workItem from="1705887937325" duration="11907000" />
      <workItem from="1705972513552" duration="12068000" />
      <workItem from="1706059035636" duration="8184000" />
      <workItem from="1706145996197" duration="4719000" />
      <workItem from="1706231955681" duration="1238000" />
      <workItem from="1706496854250" duration="1272000" />
      <workItem from="1706577647048" duration="5039000" />
      <workItem from="1706606876316" duration="2288000" />
      <workItem from="1706663256909" duration="1600000" />
      <workItem from="1706749786598" duration="7228000" />
      <workItem from="1706836686244" duration="15851000" />
      <workItem from="1706921957683" duration="5501000" />
      <workItem from="1707009742204" duration="6943000" />
      <workItem from="1708242153340" duration="606000" />
      <workItem from="1708244520105" duration="2476000" />
      <workItem from="1708247111068" duration="685000" />
      <workItem from="1708304425664" duration="5427000" />
      <workItem from="1708391638049" duration="1547000" />
      <workItem from="1708481808327" duration="2812000" />
      <workItem from="1708567880411" duration="1888000" />
      <workItem from="1708651835907" duration="5883000" />
      <workItem from="1708909686407" duration="3907000" />
      <workItem from="1709019405668" duration="2988000" />
      <workItem from="1709086017329" duration="688000" />
      <workItem from="1709088440933" duration="39000" />
      <workItem from="1709088504709" duration="96000" />
      <workItem from="1709088631004" duration="592000" />
      <workItem from="1709089455944" duration="4000" />
      <workItem from="1709089476912" duration="596000" />
      <workItem from="1709103518886" duration="14000" />
      <workItem from="1709111386392" duration="823000" />
      <workItem from="1709169573437" duration="4181000" />
      <workItem from="1709776864453" duration="587000" />
      <workItem from="1710291906402" duration="1917000" />
      <workItem from="1710378941503" duration="3149000" />
      <workItem from="1710465013695" duration="5852000" />
      <workItem from="1710750685489" duration="89000" />
      <workItem from="1719467052083" duration="593000" />
      <workItem from="1745888579284" duration="69000" />
      <workItem from="1745890609314" duration="40000" />
    </task>
    <task id="LOCAL-00001" summary="格式化">
      <option name="closed" value="true" />
      <created>1705487832548</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1705487832548</updated>
    </task>
    <task id="LOCAL-00002" summary="24.1.18 练习">
      <option name="closed" value="true" />
      <created>1705572656541</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1705572656541</updated>
    </task>
    <task id="LOCAL-00003" summary="24.1.18 练习">
      <option name="closed" value="true" />
      <created>1705572706668</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1705572706668</updated>
    </task>
    <task id="LOCAL-00004" summary="24.1.19 练习">
      <option name="closed" value="true" />
      <created>1705658615680</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1705658615680</updated>
    </task>
    <task id="LOCAL-00005" summary="24.1.22 练习grid">
      <option name="closed" value="true" />
      <created>1705918063156</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1705918063156</updated>
    </task>
    <task id="LOCAL-00006" summary="24.2.19">
      <option name="closed" value="true" />
      <created>1708308280720</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1708308280720</updated>
    </task>
    <option name="localTasksCounter" value="7" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
    <option name="exactExcludedFiles">
      <list>
        <option value="$PROJECT_DIR$/src/utils/responsive.js" />
        <option value="$PROJECT_DIR$/src/utils/responsive.js.map" />
        <option value="$PROJECT_DIR$/vite.config.js" />
        <option value="$PROJECT_DIR$/vite.config.js.map" />
      </list>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="ADD_EXTERNAL_FILES_SILENTLY" value="true" />
    <MESSAGE value="格式化" />
    <MESSAGE value="24.1.18 练习" />
    <MESSAGE value="24.1.19 练习" />
    <MESSAGE value="24.1.22 练习grid" />
    <MESSAGE value="24.2.19" />
    <option name="LAST_COMMIT_MESSAGE" value="24.2.19" />
  </component>
  <component name="VueSettings">
    <option name="innerServiceType" value="VOLAR" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>