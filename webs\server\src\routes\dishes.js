const express = require('express');
const router = express.Router();
const dishController = require('../controllers/dishController');
const { auth, adminAuth } = require('../middlewares/auth');

// 获取菜品列表
router.get('/', dishController.getDishes);

// 获取菜品分类
router.get('/categories', dishController.getCategories);

// 获取指定菜品
router.get('/:id', dishController.getDishById);

// 创建菜品 (需要管理员权限)
router.post('/', auth, adminAuth, dishController.createDish);

// 更新菜品 (需要管理员权限)
router.put('/:id', auth, adminAuth, dishController.updateDish);

// 删除菜品 (需要管理员权限)
router.delete('/:id', auth, adminAuth, dishController.deleteDish);

// 创建菜品分类 (需要管理员权限)
router.post('/categories', auth, adminAuth, dishController.createCategory);

// 更新菜品分类 (需要管理员权限)
router.put('/categories/:id', auth, adminAuth, dishController.updateCategory);

// 删除菜品分类 (需要管理员权限)
router.delete('/categories/:id', auth, adminAuth, dishController.deleteCategory);

module.exports = router;
