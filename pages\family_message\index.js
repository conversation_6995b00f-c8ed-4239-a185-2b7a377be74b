const app = getApp();
const {mockMessages} = require('../../mock/user');

Page({
  data: {
    messages: [],
    messageInput: '',
    userInfo: {}
  },

  onLoad() {
    // 获取用户信息
    if (app.globalData && app.globalData.userInfo) {
      this.setData({
        userInfo: app.globalData.userInfo
      });
    }

    // 格式化消息数据
    this.formatMessages();
  },

  // 格式化消息数据
  formatMessages() {
    const formattedMessages = mockMessages.map(msg => {
      // 根据用户ID设置不同的样式类
      const userType = msg.user_id % 2 === 0 ? 'blue' : 'pink';

      return {
        id: msg.id,
        userName: msg.user_name,
        content: msg.content,
        time: msg.created_at,
        userType
      };
    });

    this.setData({
      messages: formattedMessages
    });
  },

  // 留言输入
  onMessageInput(e) {
    this.setData({
      messageInput: e.detail.value
    });
  },

  // 添加留言
  addMessage() {
    const {messageInput, messages, userInfo} = this.data;

    if (!messageInput.trim()) {
      return;
    }

    // 获取当前时间
    const now = new Date();
    const time = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(
      2,
      '0'
    )}-${String(now.getDate()).padStart(2, '0')} ${String(
      now.getHours()
    ).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;

    // 创建新留言
    const newMessage = {
      id: messages.length + 1,
      userName: userInfo.name || '我',
      content: messageInput,
      time,
      userType: 'blue'
    };

    // 更新留言列表（新留言放在最前面）
    this.setData({
      messages: [newMessage, ...messages],
      messageInput: ''
    });

    // 提示用户
    wx.showToast({
      title: '留言已发送',
      icon: 'success',
      duration: 1500
    });
  }
});
