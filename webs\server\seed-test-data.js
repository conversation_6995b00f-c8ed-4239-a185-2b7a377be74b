require('dotenv').config();
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function seedTestData() {
  try {
    console.log('🌱 开始创建测试数据...');

    // 创建分类
    const category = await prisma.category.create({
      data: {
        name: '热菜'
      }
    });
    console.log('✅ 创建分类:', category.name);

    // 创建菜品
    const dish = await prisma.dish.create({
      data: {
        name: '红烧肉',
        description: '经典红烧肉，肥而不腻',
        categoryId: category.id
      }
    });
    console.log('✅ 创建菜品:', dish.name);

    // 创建用户
    const hashedPassword = await bcrypt.hash('123456', 10);
    const user = await prisma.user.create({
      data: {
        name: '测试用户',
        phone: '13800138000',
        password: hashedPassword,
        role: 'user'
      }
    });
    console.log('✅ 创建用户:', user.name);

    // 创建菜单
    const menu = await prisma.menu.create({
      data: {
        date: new Date(),
        isToday: true,
        remark: '今日特色菜单',
        items: {
          create: {
            dishId: dish.id,
            count: 10
          }
        }
      }
    });
    console.log('✅ 创建菜单:', menu.id);

    console.log('🎉 测试数据创建完成！');
    
    // 验证数据
    const userCount = await prisma.user.count();
    const dishCount = await prisma.dish.count();
    const categoryCount = await prisma.category.count();
    const menuCount = await prisma.menu.count();
    
    console.log(`📊 数据统计:`);
    console.log(`   用户: ${userCount} 条`);
    console.log(`   分类: ${categoryCount} 条`);
    console.log(`   菜品: ${dishCount} 条`);
    console.log(`   菜单: ${menuCount} 条`);

  } catch (error) {
    console.error('❌ 创建测试数据失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

seedTestData();
