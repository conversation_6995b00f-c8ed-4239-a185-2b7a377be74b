{"name": "pure_hl", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "cross-env NODE_OPTIONS=--max-old-space-size=4096 vite", "serve": "pnpm dev", "build": "rimraf dist && cross-env NODE_OPTIONS=--max-old-space-size=8192 vite build && generate-version-file", "preview": "vite preview", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock,build}/**/*.{vue,js,ts,tsx}\" --fix", "lint:pretty": "pretty-quick --staged", "lint:stylelint": "stylelint \"**/*.{html,vue,css,scss}\" --fix --cache --cache-location node_modules/.cache/stylelint/"}, "dependencies": {"@antv/layout": "1.2.14-beta.8", "@antv/x6": "^2.18.1", "@antv/x6-vue-shape": "^2.1.2", "@pureadmin/table": "^3.2.0", "@vueuse/core": "^10.11.1", "@vueuse/motion": "^2.2.3", "animate.css": "^4.1.1", "axios": "^1.7.4", "dayjs": "^1.11.12", "echarts": "^5.5.1", "element-plus": "^2.8.0", "immutable": "^4.3.7", "js-cookie": "^3.0.5", "mitt": "^3.0.1", "mockjs": "^1.1.0", "nprogress": "^0.2.0", "path": "^0.12.7", "petite-vue-i18n": "^9.13.1", "pinia": "^2.2.1", "qs": "^6.13.0", "responsive-storage": "^2.2.0", "sortablejs": "^1.15.2", "vue": "^3.4.38", "vue-i18n": "^9.13.1", "vue-router": "^4.4.3", "vue-types": "^5.1.3"}, "devDependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@commitlint/cli": "^18.6.1", "@commitlint/config-conventional": "^18.6.3", "@faker-js/faker": "^8.4.1", "@howdyjs/mouse-menu": "^2.1.3", "@iconify-icons/ep": "^1.2.12", "@iconify-icons/ri": "^1.2.10", "@iconify/vue": "^4.1.2", "@intlify/unplugin-vue-i18n": "^1.6.0", "@logicflow/core": "^1.2.28", "@logicflow/extension": "^1.2.28", "@pureadmin/theme": "^3.2.0", "@types/js-cookie": "^3.0.6", "@types/mockjs": "^1.0.10", "@types/node": "^20.14.15", "@types/nprogress": "^0.2.3", "@types/qrcode": "^1.5.5", "@types/qs": "^6.9.15", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-vue": "^4.6.2", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "@zxcvbn-ts/core": "^3.0.4", "autoprefixer": "^10.4.20", "china-area-data": "^5.0.1", "cloc": "1.98.0-cloc", "cropperjs": "^1.6.2", "cross-env": "^7.0.3", "cssnano": "^6.1.2", "el-table-infinite-scroll": "^3.0.6", "eslint": "^8.57.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.27.0", "gsap": "^3.12.5", "jsbarcode": "^3.11.6", "md-editor-v3": "^4.18.1", "mint-filter": "^4.0.3", "picocolors": "^1.0.1", "pinyin-pro": "^3.24.2", "postcss": "^8.4.41", "postcss-html": "^1.7.0", "postcss-import": "^15.1.0", "postcss-scss": "^4.0.9", "prettier": "^3.3.3", "pretty-quick": "^3.3.1", "qrcode": "^1.5.4", "rimraf": "^5.0.10", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.77.8", "stylelint": "^15.11.0", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^4.6.0", "stylelint-config-recommended": "^13.0.0", "stylelint-config-recommended-scss": "^13.1.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^34.0.0", "stylelint-config-standard-scss": "^11.1.0", "stylelint-order": "^6.0.4", "stylelint-prettier": "^4.1.0", "stylelint-scss": "^5.3.2", "svgo": "^3.3.2", "swiper": "^11.1.9", "tailwindcss": "^3.4.10", "terser": "^5.31.6", "tippy.js": "^6.3.7", "typeit": "^8.8.4", "typescript": "^5.5.4", "v-contextmenu": "^3.2.0", "v3-infinite-loading": "^1.3.1", "version-rocket": "^1.7.2", "vite": "^5.4.0", "vite-plugin-cdn-import": "^0.3.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-fake-server": "^2.1.1", "vite-plugin-remove-console": "^2.2.0", "vite-plugin-router-warn": "^1.0.0", "vite-svg-loader": "^5.1.0", "vue-demi": "^0.14.10", "vue-eslint-parser": "^9.4.3", "vue-json-pretty": "^2.4.0", "vue-pdf-embed": "^1.2.1", "vue-tippy": "^6.4.4", "vue-tsc": "^1.8.27", "vue-virtual-scroller": "2.0.0-beta.8", "vue-waterfall-plugin-next": "^2.4.3", "vue3-danmaku": "^1.6.1", "vuedraggable": "^4.1.0", "wavesurfer.js": "^7.8.3", "xgplayer": "^3.0.19", "xlsx": "^0.18.5"}, "pnpm": {"peerDependencyRules": {"ignoreMissing": ["rollup", "webpack", "core-js"]}, "allowedDeprecatedVersions": {"sourcemap-codec": "*", "w3c-hr-time": "*", "stable": "*"}}}