{"name": "nannan-kitchen-admin", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .vue,.js,.jsx --fix"}, "dependencies": {"@vueuse/core": "^11.2.0", "axios": "^1.7.7", "dayjs": "^1.11.13", "echarts": "^5.5.1", "element-plus": "^2.8.8", "js-cookie": "^3.0.5", "nprogress": "^0.2.0", "pinia": "^2.2.6", "vue": "^3.5.12", "vue-router": "^4.4.5"}, "devDependencies": {"@babel/core": "^7.25.8", "@babel/preset-env": "^7.25.8", "@iconify-icons/ep": "^1.2.12", "@iconify/vue": "^4.1.2", "@vitejs/plugin-vue": "^5.0.5", "@vitejs/plugin-vue-jsx": "^4.0.1", "@vue/test-utils": "^2.4.6", "@vue/vue3-jest": "^29.2.6", "autoprefixer": "^10.4.20", "babel-jest": "^29.7.0", "eslint": "^9.14.0", "eslint-plugin-vue": "^9.29.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.47", "prettier": "^3.3.3", "sass": "^1.80.6", "tailwindcss": "^3.4.14", "unplugin-auto-import": "^0.18.3", "unplugin-vue-components": "^0.27.4", "vite": "^5.4.10"}}