import { defineStore } from 'pinia'
import { login as loginApi } from '@/api/auth'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: localStorage.getItem('admin_token') || '',
    userInfo: null
  }),
  
  getters: {
    isLoggedIn: (state) => !!state.token
  },
  
  actions: {
    // 登录
    async login(credentials) {
      try {
        const response = await loginApi(credentials)
        
        if (response.code === 200) {
          this.token = response.data.token
          this.userInfo = response.data.user
          
          // 保存到本地存储
          localStorage.setItem('admin_token', this.token)
          localStorage.setItem('admin_user', JSON.stringify(this.userInfo))
          
          return { success: true }
        } else {
          return { success: false, message: response.message }
        }
      } catch (error) {
        console.error('登录失败:', error)
        return { success: false, message: '登录失败，请重试' }
      }
    },
    
    // 退出登录
    logout() {
      this.token = ''
      this.userInfo = null
      
      // 清除本地存储
      localStorage.removeItem('admin_token')
      localStorage.removeItem('admin_user')
    },
    
    // 初始化用户信息
    initUserInfo() {
      const userStr = localStorage.getItem('admin_user')
      if (userStr) {
        try {
          this.userInfo = JSON.parse(userStr)
        } catch (error) {
          console.error('解析用户信息失败:', error)
          this.logout()
        }
      }
    }
  }
})
