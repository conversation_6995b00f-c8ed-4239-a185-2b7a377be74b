Page({
  data: {
    currentType: 'hot',
    basketCount: 0,
    categories: [
      {type: 'hot', name: '热菜'},
      {type: 'cold', name: '凉菜'},
      {type: 'vegetarian', name: '素菜'},
      {type: 'meat', name: '荤菜'},
      {type: 'soup', name: '汤类'},
      {type: 'staple', name: '主食'}
    ],
    foodData: {
      hot: [
        {
          id: 1,
          name: '红烧肉',
          desc: '肥而不腻，入口即化',
          price: 28,
          img: 'https://cdn.pixabay.com/photo/2017/05/07/08/56/food-2290814_1280.jpg'
        },
        {
          id: 3,
          name: '宫保鸡丁',
          desc: '经典川菜，微辣爽口，花生香脆',
          price: 24,
          img: 'https://cdn.pixabay.com/photo/2015/04/08/13/13/food-712665_1280.jpg'
        }
      ],
      cold: [
        {
          id: 2,
          name: '清炒时蔬',
          desc: '新鲜健康，清爽可口',
          price: 16,
          img: 'https://cdn.pixabay.com/photo/2016/03/05/19/02/salad-1238248_1280.jpg'
        }
      ],
      vegetarian: [
        {
          id: 4,
          name: '鱼香茄子',
          desc: '酸甜适口，茄子软糯入味',
          price: 20,
          img: 'https://cdn.pixabay.com/photo/2016/03/05/19/02/vegetables-1238250_1280.jpg'
        },
        {
          id: 7,
          name: '蒜蓉西兰花',
          desc: '清新爽口，蒜香浓郁，健康低脂',
          price: 18,
          img: 'https://cdn.pixabay.com/photo/2016/03/05/19/02/vegetables-1238251_1280.jpg'
        }
      ],
      meat: [
        {
          id: 5,
          name: '糖醋里脊',
          desc: '外酥里嫩，酸甜开胃，老少皆宜',
          price: 26,
          img: 'https://cdn.pixabay.com/photo/2017/05/07/08/56/food-2290812_1280.jpg'
        }
      ],
      soup: [
        {
          id: 8,
          name: '紫菜蛋花汤',
          desc: '鲜美清淡，营养丰富',
          price: 12,
          img: 'https://cdn.pixabay.com/photo/2017/05/07/08/56/food-2290815_1280.jpg'
        }
      ],
      staple: [
        {
          id: 6,
          name: '番茄炒蛋',
          desc: '家常必备，酸甜可口，营养丰富',
          price: 14,
          img: 'https://cdn.pixabay.com/photo/2017/05/07/08/56/food-2290815_1280.jpg'
        }
      ]
    },
    foodList: [],
    foodDetailData: {
      1: {
        id: 1,
        name: '红烧肉',
        img: 'https://cdn.pixabay.com/photo/2017/05/07/08/56/food-2290814_1280.jpg',
        remark: '肥而不腻，入口即化',
        material: '五花肉、酱油、糖、料酒、葱姜蒜',
        method:
          '1. 五花肉切块焯水；2. 炒糖色，加入五花肉翻炒；3. 加调料炖煮至软烂收汁。'
      },
      2: {
        id: 2,
        name: '清炒时蔬',
        img: 'https://cdn.pixabay.com/photo/2016/03/05/19/02/salad-1238248_1280.jpg',
        remark: '新鲜健康，清爽可口',
        material: '时令蔬菜、蒜、盐、油',
        method:
          '1. 蔬菜洗净切段；2. 热锅凉油爆香蒜末；3. 下蔬菜大火快炒，加盐出锅。'
      },
      3: {
        id: 3,
        name: '宫保鸡丁',
        img: 'https://cdn.pixabay.com/photo/2015/04/08/13/13/food-712665_1280.jpg',
        remark: '经典川菜，微辣爽口，花生香脆',
        material: '鸡胸肉、花生、干辣椒、花椒、葱姜蒜',
        method:
          '1. 鸡丁腌制；2. 炒香花生备用；3. 炒鸡丁加配料，最后加花生翻炒均匀。'
      },
      4: {
        id: 4,
        name: '鱼香茄子',
        img: 'https://cdn.pixabay.com/photo/2016/03/05/19/02/vegetables-1238250_1280.jpg',
        remark: '酸甜适口，茄子软糯入味',
        material: '茄子、肉末、豆瓣酱、糖、醋、蒜',
        method: '1. 茄子切条炸软；2. 炒肉末加调料；3. 下茄子翻炒收汁。'
      },
      5: {
        id: 5,
        name: '糖醋里脊',
        img: 'https://cdn.pixabay.com/photo/2017/05/07/08/56/food-2290812_1280.jpg',
        remark: '外酥里嫩，酸甜开胃，老少皆宜',
        material: '里脊肉、淀粉、糖、醋、番茄酱',
        method: '1. 里脊切条挂糊炸至金黄；2. 炒糖醋汁，倒入里脊翻匀。'
      },
      6: {
        id: 6,
        name: '番茄炒蛋',
        img: 'https://cdn.pixabay.com/photo/2017/05/07/08/56/food-2290815_1280.jpg',
        remark: '家常必备，酸甜可口，营养丰富',
        material: '番茄、鸡蛋、盐、糖',
        method: '1. 鸡蛋炒熟盛出；2. 番茄炒软加蛋回锅，调味即可。'
      },
      7: {
        id: 7,
        name: '蒜蓉西兰花',
        img: 'https://cdn.pixabay.com/photo/2016/03/05/19/02/vegetables-1238251_1280.jpg',
        remark: '清新爽口，蒜香浓郁，健康低脂',
        material: '西兰花、大蒜、盐、油',
        method: '1. 西兰花焯水；2. 爆香蒜末，下西兰花快炒，加盐出锅。'
      },
      8: {
        id: 8,
        name: '紫菜蛋花汤',
        img: 'https://cdn.pixabay.com/photo/2017/05/07/08/56/food-2290815_1280.jpg',
        remark: '鲜美清淡，营养丰富',
        material: '紫菜、鸡蛋、葱花、盐、香油',
        method: '1. 水开下紫菜，打入蛋液搅匀；2. 加盐、香油、葱花即可。'
      }
    }
  },

  onLoad() {
    // 初始化默认分类的菜品列表
    this.renderFoodList('hot');
    // 更新购物篮数量
    this.updateBasketCount();
  },

  onShow() {
    // 页面显示时更新购物篮数量
    this.updateBasketCount();
  },

  // 切换分类
  switchCategory(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      currentType: type
    });
    this.renderFoodList(type);
  },

  // 渲染菜品列表
  renderFoodList(type) {
    const foodList = this.data.foodData[type] || [];

    // 为每个菜品添加 isAdding 属性，用于控制点击动画
    const enhancedFoodList = foodList.map(item => ({
      ...item,
      isAdding: false
    }));

    this.setData({
      foodList: enhancedFoodList
    });
  },

  // 跳转到菜品详情页
  goToDetail(e) {
    const id = e.currentTarget.dataset.id;
    const detailData = this.data.foodDetailData[id];

    // 将详情数据存入缓存
    wx.setStorageSync('detailData', detailData);

    // 跳转到详情页
    wx.navigateTo({
      url: '/pages/detail/index'
    });
  },

  // 添加到购物篮
  addToBasket(e) {
    const {id, name, desc, img, index} = e.currentTarget.dataset;

    // 获取购物篮数据
    let basket = wx.getStorageSync('basket') || {};

    if (!basket[id]) {
      basket[id] = {id, name, desc, img, count: 1};
    } else {
      basket[id].count += 1;
    }

    // 保存购物篮数据
    wx.setStorageSync('basket', basket);

    // 更新购物篮数量
    this.updateBasketCount();

    // 显示添加动画
    const foodList = [...this.data.foodList];
    foodList[index].isAdding = true;
    this.setData({foodList});

    // 动画结束后恢复
    setTimeout(() => {
      foodList[index].isAdding = false;
      this.setData({foodList});
    }, 600);

    // 提示用户
    wx.showToast({
      title: '已添加到购物篮',
      icon: 'success',
      duration: 1000
    });
  },

  // 更新购物篮数量
  updateBasketCount() {
    const basket = wx.getStorageSync('basket') || {};
    const total = Object.values(basket).reduce(
      (sum, item) => sum + item.count,
      0
    );

    this.setData({
      basketCount: total
    });
  },

  // 跳转到购物篮页面
  goToBasket() {
    wx.navigateTo({
      url: '/pages/today_order/index'
    });
  }
});
