require('dotenv').config();
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testDatabase() {
  try {
    console.log('🔍 Testing database connection...');
    console.log('📍 Database URL:', process.env.DATABASE_URL?.replace(/:[^:@]*@/, ':****@'));
    
    // 测试连接
    await prisma.$connect();
    console.log('✅ Database connection successful!');
    
    // 检查所有表的数据
    const tables = ['user', 'category', 'dish', 'menu', 'menuItem', 'order', 'message', 'notification'];
    
    for (const table of tables) {
      try {
        const count = await prisma[table].count();
        console.log(`📊 ${table} table: ${count} records`);
        
        if (count > 0) {
          const sample = await prisma[table].findFirst();
          console.log(`   Sample record:`, JSON.stringify(sample, null, 2));
        }
      } catch (error) {
        console.log(`❌ Error accessing ${table} table:`, error.message);
      }
    }
    
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    console.error('Full error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testDatabase();
