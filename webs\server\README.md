# 楠楠家厨小程序后端

这是楠楠家厨微信小程序的后端服务，基于 Express.js 和 Prisma 构建。

### 一键启动（推荐）
```bash
cd webs/server
npm run quick-start
```

### 手动启动
```bash
cd webs/server
npm run generate    # 生成 Prisma 客户端
npm run db:push     # 同步数据库模式
npm run dev         # 启动开发服务器
npm run studio      # 启动 Prisma Studio（可选）
```

## 📋 常用命令速查

| 命令                         | 说明               |
| ---------------------------- | ------------------ |
| `npm run quick-start`        | 一键启动所有服务   |
| `npm run dev`                | 启动开发服务器     |
| `npm run studio`             | 启动 Prisma Studio |
| `npm run db:test`            | 测试数据库连接     |
| `npm run db:export`          | 导出数据库数据     |
| `npm run db:import <文件名>` | 导入数据库数据     |
| `npm run db:clear`           | 清空所有数据       |
| `npm run db:seed`            | 创建测试数据       |

## 技术栈

- **Node.js** - JavaScript 运行时
- **Express.js** - Web 框架
- **Prisma** - ORM
- **console.neon.tech** - MySQL 兼容的云数据库
- **PicX** - 图床服务
- **Vercel** - 部署平台

## 功能

- 用户认证（账号密码登录和微信登录）
- 菜单管理
- 菜品管理
- 订单管理
- 消息系统
- 通知系统
- 图片上传

## 开始使用

### 前提条件

- Node.js 14+
- npm 或 yarn
- console.neon.tech 账号
- PicX 账号（GitHub 仓库）
- Vercel 账号（可选，用于部署）

### 安装

1. 克隆仓库
```bash
git clone <repository-url>
cd wx-nan/webs/server
```

2. 安装依赖
```bash
npm install
# 或
yarn install
```

3. 配置环境变量
复制 `.env.example` 文件为 `.env`，并填写相应的配置：
```
# 数据库连接
DATABASE_URL="mysql://user:<EMAIL>/wx-nan?sslaccept=strict"

# JWT 配置
JWT_SECRET="your-jwt-secret-key"
JWT_EXPIRES_IN="7d"

# 服务器配置
PORT=3000
NODE_ENV="development"

# PicX 配置
PICX_TOKEN="your-github-token"
PICX_REPO="your-github-username/your-picx-repo"

# 微信小程序配置
WECHAT_APPID="your-wechat-appid"
WECHAT_SECRET="your-wechat-secret"
```

4. 初始化数据库
```bash
npx prisma generate
npx prisma db push
```

5. 启动开发服务器
```bash
npm run dev
# 或
yarn dev
```

### 部署到 Vercel

1. 安装 Vercel CLI
```bash
npm install -g vercel
```

2. 登录 Vercel
```bash
vercel login
```

3. 部署
```bash
vercel
```

## API 文档

### 认证 API

- `POST /api/auth/login` - 账号密码登录
- `POST /api/auth/register` - 注册新用户
- `GET /api/auth/me` - 获取当前用户信息

### 用户 API

- `GET /api/users` - 获取用户列表
- `GET /api/users/family` - 获取家庭成员列表
- `GET /api/users/:id` - 获取指定用户
- `POST /api/users` - 创建用户
- `PUT /api/users/:id` - 更新用户
- `DELETE /api/users/:id` - 删除用户

### 菜单 API

- `GET /api/menus` - 获取菜单列表
- `GET /api/menus/today` - 获取今日菜单
- `GET /api/menus/history` - 获取历史菜单
- `GET /api/menus/:id` - 获取指定菜单
- `POST /api/menus` - 创建菜单
- `PUT /api/menus/:id` - 更新菜单
- `DELETE /api/menus/:id` - 删除菜单

### 菜品 API

- `GET /api/dishes` - 获取菜品列表
- `GET /api/dishes/categories` - 获取菜品分类
- `GET /api/dishes/:id` - 获取指定菜品
- `POST /api/dishes` - 创建菜品
- `PUT /api/dishes/:id` - 更新菜品
- `DELETE /api/dishes/:id` - 删除菜品
- `POST /api/dishes/categories` - 创建菜品分类
- `PUT /api/dishes/categories/:id` - 更新菜品分类
- `DELETE /api/dishes/categories/:id` - 删除菜品分类

### 订单 API

- `GET /api/orders` - 获取订单列表
- `GET /api/orders/today` - 获取今日订单
- `GET /api/orders/:id` - 获取指定订单
- `POST /api/orders` - 创建订单
- `PUT /api/orders/:id` - 更新订单
- `DELETE /api/orders/:id` - 删除订单

### 消息 API

- `GET /api/messages` - 获取消息列表
- `GET /api/messages/:id` - 获取指定消息
- `POST /api/messages` - 创建消息
- `PUT /api/messages/:id` - 更新消息
- `DELETE /api/messages/:id` - 删除消息

### 通知 API

- `GET /api/notifications` - 获取通知列表
- `GET /api/notifications/:id` - 获取指定通知
- `POST /api/notifications` - 创建通知
- `PUT /api/notifications/:id` - 更新通知
- `DELETE /api/notifications/:id` - 删除通知

### 上传 API

- `POST /api/upload/image` - 上传图片

## 许可证

[MIT](LICENSE)
